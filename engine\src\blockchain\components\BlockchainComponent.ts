/**
 * 区块链组件 - 为实体添加区块链相关功能
 */

import { Component } from '../../core/Component';
import { 
  BlockchainState, 
  Transaction, 
  SmartContractCall,
  BlockchainEventType 
} from '../types/BlockchainTypes';

export interface BlockchainComponentConfig {
  enableTransactionHistory?: boolean;
  enableContractInteraction?: boolean;
  enableEventListening?: boolean;
  maxTransactionHistory?: number;
  contractAddresses?: string[];
  eventFilters?: any[];
}

export class BlockchainComponent extends Component {
  // 配置
  protected blockchainConfig: BlockchainComponentConfig;

  // 状态
  private isConnected: boolean = false;
  private currentNetwork: string | null = null;
  private currentAccount: string | null = null;

  // 交易历史
  private transactions: Transaction[] = [];
  private pendingTransactions: Set<string> = new Set();

  // 合约调用历史
  private contractCalls: SmartContractCall[] = [];

  // 事件监听
  private eventListeners: Map<string, Function[]> = new Map();
  private lastEventBlock: number = 0;

  constructor(config: BlockchainComponentConfig = {}) {
    super('BlockchainComponent');

    this.blockchainConfig = {
      enableTransactionHistory: true,
      enableContractInteraction: true,
      enableEventListening: true,
      maxTransactionHistory: 100,
      contractAddresses: [],
      eventFilters: [],
      ...config
    };
  }

  /**
   * 组件初始化
   */
  initialize(): void {
    console.log('初始化区块链组件');
    
    // 设置默认状态
    this.reset();
  }

  /**
   * 组件更新
   */
  update(deltaTime: number): void {
    super.update(deltaTime);

    // 清理过期的待处理交易
    this.cleanupPendingTransactions();

    // 限制交易历史大小
    this.limitTransactionHistory();
  }

  /**
   * 设置连接状态
   */
  setConnectionState(isConnected: boolean, account?: string, network?: string): void {
    this.isConnected = isConnected;
    this.currentAccount = account || null;
    this.currentNetwork = network || null;
    
    if (!isConnected) {
      this.reset();
    }
  }

  /**
   * 添加交易
   */
  addTransaction(transaction: Transaction): void {
    if (!this.blockchainConfig.enableTransactionHistory) {
      return;
    }

    // 检查是否已存在
    const existingIndex = this.transactions.findIndex(tx => tx.hash === transaction.hash);
    if (existingIndex !== -1) {
      // 更新现有交易
      this.transactions[existingIndex] = transaction;
    } else {
      // 添加新交易
      this.transactions.unshift(transaction);
    }

    // 如果是待处理交易，添加到待处理集合
    if (transaction.status === 'pending') {
      this.pendingTransactions.add(transaction.hash);
    } else {
      this.pendingTransactions.delete(transaction.hash);
    }

    this.limitTransactionHistory();
  }

  /**
   * 更新交易状态
   */
  updateTransactionStatus(txHash: string, status: 'pending' | 'confirmed' | 'failed', blockNumber?: number): void {
    const transaction = this.transactions.find(tx => tx.hash === txHash);
    if (transaction) {
      transaction.status = status;
      if (blockNumber) {
        transaction.blockNumber = blockNumber;
      }
      
      if (status !== 'pending') {
        this.pendingTransactions.delete(txHash);
      }
    }
  }

  /**
   * 添加合约调用
   */
  addContractCall(contractCall: SmartContractCall): void {
    if (!this.blockchainConfig.enableContractInteraction) {
      return;
    }

    this.contractCalls.unshift(contractCall);

    // 限制合约调用历史大小
    if (this.contractCalls.length > (this.blockchainConfig.maxTransactionHistory || 100)) {
      this.contractCalls = this.contractCalls.slice(0, this.blockchainConfig.maxTransactionHistory);
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: string, callback: Function): void {
    if (!this.blockchainConfig.enableEventListening) {
      return;
    }

    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    
    this.eventListeners.get(eventType)!.push(callback);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: string, callback: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  emitEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 获取交易历史
   */
  getTransactions(): Transaction[] {
    return [...this.transactions];
  }

  /**
   * 获取待处理交易
   */
  getPendingTransactions(): Transaction[] {
    return this.transactions.filter(tx => tx.status === 'pending');
  }

  /**
   * 获取合约调用历史
   */
  getContractCalls(): SmartContractCall[] {
    return [...this.contractCalls];
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): {
    isConnected: boolean;
    account: string | null;
    network: string | null;
  } {
    return {
      isConnected: this.isConnected,
      account: this.currentAccount,
      network: this.currentNetwork
    };
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalTransactions: number;
    pendingTransactions: number;
    confirmedTransactions: number;
    failedTransactions: number;
    totalContractCalls: number;
    activeEventListeners: number;
  } {
    const confirmedTxs = this.transactions.filter(tx => tx.status === 'confirmed').length;
    const failedTxs = this.transactions.filter(tx => tx.status === 'failed').length;
    const totalListeners = Array.from(this.eventListeners.values())
      .reduce((total, listeners) => total + listeners.length, 0);

    return {
      totalTransactions: this.transactions.length,
      pendingTransactions: this.pendingTransactions.size,
      confirmedTransactions: confirmedTxs,
      failedTransactions: failedTxs,
      totalContractCalls: this.contractCalls.length,
      activeEventListeners: totalListeners
    };
  }

  /**
   * 检查是否有待处理交易
   */
  hasPendingTransactions(): boolean {
    return this.pendingTransactions.size > 0;
  }

  /**
   * 检查特定交易是否存在
   */
  hasTransaction(txHash: string): boolean {
    return this.transactions.some(tx => tx.hash === txHash);
  }

  /**
   * 获取特定交易
   */
  getTransaction(txHash: string): Transaction | null {
    return this.transactions.find(tx => tx.hash === txHash) || null;
  }

  /**
   * 清理过期的待处理交易
   */
  private cleanupPendingTransactions(): void {
    const now = Date.now();
    const timeout = 10 * 60 * 1000; // 10分钟超时
    
    for (const txHash of this.pendingTransactions) {
      const transaction = this.transactions.find(tx => tx.hash === txHash);
      if (transaction && transaction.timestamp) {
        if (now - transaction.timestamp > timeout) {
          // 标记为失败
          this.updateTransactionStatus(txHash, 'failed');
        }
      }
    }
  }

  /**
   * 限制交易历史大小
   */
  private limitTransactionHistory(): void {
    const maxHistory = this.blockchainConfig.maxTransactionHistory || 100;
    if (this.transactions.length > maxHistory) {
      this.transactions = this.transactions.slice(0, maxHistory);
    }
  }

  /**
   * 重置组件状态
   */
  private reset(): void {
    this.transactions = [];
    this.pendingTransactions.clear();
    this.contractCalls = [];
    this.eventListeners.clear();
    this.lastEventBlock = 0;
  }

  /**
   * 序列化组件数据
   */
  serialize(): any {
    return {
      type: this.getType(),
      config: this.blockchainConfig,
      isConnected: this.isConnected,
      currentNetwork: this.currentNetwork,
      currentAccount: this.currentAccount,
      transactions: this.transactions,
      contractCalls: this.contractCalls,
      lastEventBlock: this.lastEventBlock
    };
  }

  /**
   * 反序列化组件数据
   */
  deserialize(data: any): void {
    if (data.config) {
      this.blockchainConfig = { ...this.blockchainConfig, ...data.config };
    }
    
    this.isConnected = data.isConnected || false;
    this.currentNetwork = data.currentNetwork || null;
    this.currentAccount = data.currentAccount || null;
    this.transactions = data.transactions || [];
    this.contractCalls = data.contractCalls || [];
    this.lastEventBlock = data.lastEventBlock || 0;
    
    // 重建待处理交易集合
    this.pendingTransactions.clear();
    this.transactions
      .filter(tx => tx.status === 'pending')
      .forEach(tx => this.pendingTransactions.add(tx.hash));
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): BlockchainComponent {
    return new BlockchainComponent(this.blockchainConfig);
  }

  /**
   * 组件销毁
   */
  destroy(): void {
    this.reset();
    console.log('区块链组件已销毁');
  }
}
